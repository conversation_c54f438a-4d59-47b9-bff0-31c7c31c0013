import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableColumn,
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { TableInputType } from "../../enums";
import { mockData } from "./usuariosMockData";

type UserProps = {};

const URL_USER = "v1/services/treasury/user-maintenance";

const Usuarios: FC<UserProps> = () => {

  const userCol: TableColumn[] = [
    {
      id: "table-head-1",
      label: "ID USUARIO",
      accessor: "id",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.TEXT,
      isCompositeKey: false,
    },
    {
      id: "table-head-2",
      label: "NOMBRE",
      accessor: "tusuNomUsuario",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    /*{
      id: "table-head-4",
      label: "FECHA DESDE",
      accessor: "tusuFecIniUsr",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-5",
      label: "FECHA HASTA",
      accessor: "tusuFecFinUsr",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-6",
      label: "ROL",
      accessor: "tusuTrolClave",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-7",
      label: "NUMERO DE EMPLEADO",
      accessor: "tusuNumEmpleado",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-8",
      label: "STATUS",
      accessor: "tusuNumEmpleado",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-9",
      label: "STATUS",
      accessor: "tusuNumEmpleado",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-10",
      label: "STATUS",
      accessor: "tusuNumEmpleado",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-11",
      label: "STATUS",
      accessor: "tusuNumEmpleado",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },*/
    /*{
      id: "table-head-8",
      label: "ESTADO",
      accessor: "TCME_NOM_COMPONENTE",
      isLink: false,
      sortable: true,
      type: TableInputType.DATE,
    },
    {
      id: "table-head-9",
      label: "NOMBRE OBJETO FISICO PANTALLA",
      accessor: "TCME_VER_COMPONENTE",
      isLink: false,
      sortable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-10",
      label: "FECHA MOD",
      accessor: "TCIA_FEC_MOD",
      isLink: false,
      sortable: true,
      type: TableInputType.DATE,
    },*/
  ];

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
      
    const { result: responsePost } = usePost(callPostAPI);
    const { result, loading, error, refetch } = useGet<TableResponse>({
      url: URL_USER,
      call: true,
    });
    const onSaveUser = (newElement: TableData) => {
      setCallPostAPI({
        url: URL_USER,
        call: true,
        data: newElement,
      });
    };
    const onEditUser = (editElement: TableData) => {
                setCallPostAPI({
                  url: `${URL_USER}/${editElement?.id}`,
                  call: true,
                  method: "PUT",
                  data: editElement,
                });
              };
              const onDeleteUser = (
                params: { type: "composite"; [key:string]: string | number | undefined } | {type: "id"; id: number}) => {
                if(params.type === "composite") {
                  const compositeKeys = userCol
                  .filter(col => col.isCompositeKey)
                  .map(col => col.accessor);
                  const values = compositeKeys.map(key => params[key]);
                  if (values.some(v => v === undefined)) {
                    console.error("Faltan valores en la clave compuesta: ", compositeKeys, params);
                    return;
                  }
                  const safeValues = values as (string | number)[];
                  const urlPath = safeValues.join("/");
                  console.log("" + URL_USER+"/" +urlPath);  
                  setCallPostAPI({
                    url: `${URL_USER}/${urlPath}`,
                    call: true,
                    method: "DELETE",
                  });
                } else if (params.type === "id"){
                  setCallPostAPI({
                    url: `${URL_USER}/${params.id}`,
                    call: true,
                    method: "DELETE",
                  });
                }
              };

    useEffect(() => {
      if (responsePost != null) {
        refetch();
        setCallPostAPI(initialPostApi);
      }
    }, [responsePost, refetch]);
  
    if (loading)
      return (
        <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
      );
    if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE USUARIOS</h1>
      <Table
        id="menu"
        data={result?.data ?? []}
        columns={userCol}
        filters={["tusuCdgUsuario", "tusuNomUsuario"]}
        onSave={onSaveUser}
        onEdit={onEditUser}
        onDelete={onDeleteUser}
      />
    </div>
  );
};

export default Usuarios;
