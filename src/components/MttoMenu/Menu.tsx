import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableColumn,
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { TableInputType } from "../../enums";
import { mockData } from "./menuMockData";

type MenuProps = {};

const URL_MENU = "v1/services/treasury/menu-maintenance";

const Menu: FC<MenuProps> = () => {

  const menuCol: TableColumn[] = [
    {
      id: "table-head-1",
      label: "ID",
      accessor: "id",
      isLink: false,
      sortable: true,
      hidden: true,
      editable: false,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-2",
      label: "MODULO",
      accessor: "tcmeCodModulo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-3",
      label: "NIVEL",
      accessor: "tcmeIdNivel",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-4",
      label: "CLAVE DE NODO MAESTRO",
      accessor: "tcmeNodoMaestro",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-5",
      label: "CLAVE DE NODO DEPENDIENTE",
      accessor: "tcmeNodoDependiente",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-6",
      label: "DESCRIPCION DE LA OPCION",
      accessor: "tcmeDescripcion",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-7",
      label: "NOMBRE OBJETO FISICO",
      accessor: "tcmeCodMenu",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-8",
      label: "NOMBRE OBJETO FISICO DEPENDIENTE",
      accessor: "tcmeCogSubMenu",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-9",
      label: "ESTADO",
      accessor: "tcmeActivo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-10",
      label: "NOMBRE OBJETO FISICO PANTALLA",
      accessor: "tcmeNomComponente",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-11",
      label: "USUARIO CREO",
      accessor: "tcmeUserCreo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.TEXT,
    },
    /*{
      id: "table-head-12",
      label: "FECHA CREO",
      accessor: "tcmeFecCreo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.DATE_TIME,
    },*/
    /*{
      id: "table-head-13",
      label: "USUARIO MOD",
      accessor: "tcmeUserMod",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-14",
      label: "FECHA MOD",
      accessor: "tcmeFecMod",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.DATE_TIME,
    },*/
  ];

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
    
      const { result: responsePost } = usePost(callPostAPI);
      const { result, loading, error, refetch } = useGet<TableResponse>({
        url: URL_MENU,
        call: true,
      });
      const onSaveMenu = (newElement: TableData) => {
        setCallPostAPI({
          url: URL_MENU,
          call: true,
          data: newElement,
        });
      };
      const onEditMenu = (editElement: TableData) => {
            setCallPostAPI({
              url: `${URL_MENU}/${editElement?.id}`,
              call: true,
              method: "PUT",
              data: editElement,
            });
          };
          const onDeleteMenu = (
            params: { type: "composite"; [key:string]: string | number | undefined } | {type: "id"; id: number}) => {
            if(params.type === "composite") {
              const compositeKeys = menuCol
              .filter(col => col.isCompositeKey)
              .map(col => col.accessor);
              const values = compositeKeys.map(key => params[key]);
              if (values.some(v => v === undefined)) {
                console.error("Faltan valores en la clave compuesta: ", compositeKeys, params);
                return;
              }
              const safeValues = values as (string | number)[];
              const urlPath = safeValues.join("/");
              console.log("" + URL_MENU+"/" +urlPath);  
              setCallPostAPI({
                url: `${URL_MENU}/${urlPath}`,
                call: true,
                method: "DELETE",
              });
            } else if (params.type === "id"){
              setCallPostAPI({
                url: `${URL_MENU}/${params.id}`,
                call: true,
                method: "DELETE",
              });
            }
          };
  
      useEffect(() => {
          if (responsePost != null) {
            refetch();
            setCallPostAPI(initialPostApi);
          }
        }, [responsePost, refetch]);
      
      if (loading)
          return (
            <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
          );
        if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE MENU</h1>
      <Table
        id="menu"
        data={result?.data ?? []}
        columns={menuCol}
        filters={["tcmeCodModulo", "tcmeIdNivel", "tcmeNodoMaestro"]}
        onSave={onSaveMenu}
        onEdit={onEditMenu}
        onDelete={onDeleteMenu}
      />
    </div>
  );
};

export default Menu;
