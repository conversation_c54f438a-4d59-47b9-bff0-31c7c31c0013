import { FC, useState, useEffect } from "react";
import MLDCReactDataTable from "@metlife/mldc-react-data-table";
import TableSlidePanel from "./TableSlidePanel";
import TableFilters from "./TableFilters";
import { TableColumn, TableData } from "./table-types";
import { TableInputType } from "../../../enums";
import "./table.css";

type TableDeleteParams =
  | { type: "id"; id: number }
  | { type: "composite"; [key: string]: string | number | undefined };

type TableProps = {
  id: string;
  data: TableData[];
  filters?: string[];
  columns: TableColumn[];
  onSave?: (element: TableData) => void;
  onEdit?: (element: TableData) => void;
  onDelete?: (params: TableDeleteParams) => void;
  enableAddRow?: boolean;
};

const formatterDate = new Intl.DateTimeFormat("es-MX", {
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
});

const formatterDateTime = new Intl.DateTimeFormat("es-MX", {
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
  hour: "2-digit",
  minute: "2-digit",
  second: "2-digit",
});

const parseDate = (dateString: string): Date | null => {
  if (!dateString) return null;

  const ddMMyyyyPattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = dateString.match(ddMMyyyyPattern);

  if (match) {
    const [, day, month, year] = match;
    const parsedDate = new Date(
      parseInt(year),
      parseInt(month) - 1,
      parseInt(day)
    );

    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    }
  }

  const directDate = new Date(dateString);
  if (!isNaN(directDate.getTime())) {
    return directDate;
  }

  return null;
};

const Table: FC<TableProps> = ({
  id,
  data,
  filters,
  columns,
  onSave,
  onEdit,
  onDelete,
  enableAddRow,
}) => {
  const [editedData, setEditedData] = useState<TableData>();
  const [openPanel, setOpenPanel] = useState<boolean>(false);
  const [isEditingRow, setIsEditingRow] = useState<boolean>(false);
  const [filteredData, setFilteredData] = useState<TableData[]>(data);

  // Sync filteredData with external data changes
  useEffect(() => {
    setFilteredData(data);
  }, [data]);

  const showingColumns = columns.filter((col) => !col.hidden);
  const editableColumns = columns.filter((col) => {
    if (col.showInFormOnly !== undefined) {
      return col.showInFormOnly;
    }

    return col.editable || col.showInNew || isEditingRow;
  });

  const formattedSelectData = (data: TableData) => {
    return columns
      .filter((c) => c.type === TableInputType.SELECT)
      .map((c) => ({
        [c.accessor]: data?.[c.accessor]
          ? (c.displayOptions?.find((o) => o.value === data?.[c.accessor])
              ?.label as string)
          : "",
      }))
      .reduce((acc, obj) => ({ ...acc, ...obj }), {});
  };

  const formattedDateTimeData = (data: TableData) => {
    return columns
      .filter(
        (c) =>
          c.type === TableInputType.DATE || c.type === TableInputType.DATE_TIME
      )
      .map((c) => {
        const dateValue = data?.[c.accessor] as string;
        if (!dateValue) {
          return { [c.accessor]: "" };
        }

        const parsedDate = parseDate(dateValue);
        if (!parsedDate) {
          return { [c.accessor]: dateValue };
        }

        return {
          [c.accessor]:
            c.type === TableInputType.DATE
              ? formatterDate.format(parsedDate)
              : formatterDateTime.format(parsedDate),
        };
      })
      .reduce((acc, obj) => ({ ...acc, ...obj }), {});
  };

  const formattedData = filteredData.map((data) => {
    return {
      ...data,
      ...formattedSelectData(data),
      ...formattedDateTimeData(data),
    };
  });
  const totalColumns = columns.filter((col) => col.isTotal);
  const totalRow = totalColumns.reduce(
    (acc, col) => {
      const total = filteredData.reduce((sum, row) => {
        const value = parseFloat(
          String(row[col.accessor] || "").replace(/[^\d.-]/g, "")
        );
        return sum + (isNaN(value) ? 0 : value);
      }, 0);
      return {
        ...acc,
        [col.accessor]: "$" + total.toFixed(2),
      };
    },
    {
      id: "- ",
      CONCEPTO: "TOTAL",
      className: "total-row",
    }
  );
  const dataWithTotal = [...formattedData, totalRow];

  const _onDeleteAction = (dataId: number) => {
      const row = filteredData.find((r) => r.id === dataId);
      if(!row) return;

      //const compositeKeys = ["bankId","companyId","cuenta"];
      const compositeKeys = columns.filter((col) => col.isCompositeKey).map((col) => col.accessor);
      const hasCompositeKeys = compositeKeys.length>0;
      //const hasCompositeKeys = columns.some((col) =>
        //compositeKeys.includes(col.accessor));
      if (hasCompositeKeys) {
        const deleteParams: TableDeleteParams = {
          type: "composite",
          ...compositeKeys.reduce((acc, key) => {
            const value = row[key];
            if (typeof value === "string" || typeof value === "number"){
              acc[key] = value;
            } 
            return acc;
          }, {} as Record<string, string | number | undefined>),
        };
        setFilteredData(
          filteredData.filter((r) =>
            compositeKeys.some((key) => r[key] !== row[key]))
        );
        onDelete?.(deleteParams);
      } else {
        const deleteParams: TableDeleteParams = { type: "id", id:dataId };
        setFilteredData(filteredData.filter((item) => item.id !== dataId));
        onDelete?.(deleteParams);
      }
    //const updated = filteredData.filter((item) => item.id !== dataId);

    //setFilteredData(updated);
  };

  const _onEditActionButton = (dataId: number) => {
    setEditedData(filteredData.find((d) => d.id === dataId));
    setOpenPanel(true);
  };

  const _onSaveClick = (isEditing: boolean) => {
    const element = editableColumns.reduce((acc, col) => {
      const value =
        col.type === TableInputType.SELECT
          ? col.displayOptions?.find(
              (opt) =>
                opt.label ===
                (
                  document.getElementById(
                    `${col.id}-combo-label`
                  ) as HTMLSpanElement
                )?.textContent
            )?.value
          : (document.getElementById(`${col.id}-input`) as HTMLInputElement)
              ?.value;
      return {
        ...acc,
        [col.accessor]: value,
      };
    }, {});
    !isEditing ? onSave?.(element) : onEdit?.(element);
    setOpenPanel(false);
  };

  const removeActionIcons = !(!onEdit && !onDelete);

  const getRemoveActionsClassName = () => {
    const editClassName = onEdit ? "" : "table-without-edit";
    const deleteClassName = onDelete ? "" : "table-without-delete";
    return `${editClassName} ${deleteClassName}`;
  };

  return (
    <>
      {filters && (
        <TableFilters
          data={data}
          columns={columns}
          filters={filters}
          onFilterChange={setFilteredData}
        />
      )}
      <MLDCReactDataTable
        id={`${id}-data-table`}
        data={totalColumns.length > 0 ? dataWithTotal : formattedData}
        columns={showingColumns}
        actionIcons={removeActionIcons}
        enableAddRow={enableAddRow === false ? false : true}
        pagination={true}
        disableStackedView={true}
        onAddRow={() => {
          setIsEditingRow(false);
          setOpenPanel(true);
        }}
        onEditRow={(_, dataId: number) => {
          setIsEditingRow(true);
          _onEditActionButton(dataId);
        }}
        onDeleteRow={(_, dataId: number) => {
          _onDeleteAction(dataId);
        }}
        style={{ maxHeight: "70vh", minHeight: "35vh" }}
        className={getRemoveActionsClassName()}
      />
      <TableSlidePanel
        data={editedData}
        columns={editableColumns}
        openPanel={openPanel}
        isEditingRow={isEditingRow}
        setOpenPanel={setOpenPanel}
        onSave={() => _onSaveClick(isEditingRow)}
      />
    </>
  );
};
export default Table;
