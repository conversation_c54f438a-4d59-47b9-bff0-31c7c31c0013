.mldc-data-table.content-box {
  overflow: auto;

  .delete-tooltip-wrapper {
    .deleteOverlay {
      visibility: hidden;
    }

    .mldc-button.primary.blue.primary span {
      visibility: hidden;
    }

    .mldc-button.primary.blue.primary span:after {
      content: "Si";
      color: var(--brand-neutrals-white);
      visibility: visible;
      margin-left: -24px;
    }
  }

  .deleteOverlay:after {
    display: block;
    margin-top: -20px;
    content: "¿Seguro desea eliminar?";
    position: relative;
    visibility: visible;
    font-size: var(--px-size-16);
    font-weight: var(--weight-6);
    color: var(--brand-neutrals-gray-darkest);
  }
}

.calendar-container .mldc-calendar .footer,
.calender-container .mldc-calendar .footer {
  display: none;
}

.mldc-input-date-picker-container
  .mldc-input-date-picker-group
  .input-group.with-icon {
  padding-right: 22px;
}

.add-new-wrapper a {
  visibility: hidden;
}

.add-new-wrapper a span {
  visibility: visible;
}

.add-new-wrapper a:after {
  left: -37px;
  content: "Agregar";
  position: relative;
  visibility: visible;
  font: normal normal var(--weight-6) 16px / 24px var(--family-noto);
}

.table-without-edit .mldc-data-table table tbody tr td,
.table-without-delete .mldc-data-table table tbody tr td {
  div[role="row"] {
    justify-self: center;
  }
}

.table-without-delete .mldc-data-table table tbody tr td {
  div[role="row"] div:last-child {
    display: none !important;
  }
}

.table-without-edit .mldc-data-table .action-cell {
  display: none !important;
}

.mldc-data-table table thead tr th:last-child .aligner .header-text {
  visibility: hidden;
}
mldc-data-table table tbody tr:last-child td,
.mldc-data-table .content-box table tbody tr:last-child td,
.table-header {
  gap: 10px;
  display: flex;
  flex-direction: row;
  justify-content: center;

  .mldc-input-number-container {
    width: inherit;
  }

  .button-clear-filters {
    cursor: pointer;
    align-content: center;
  }

  .button-clear-filters.disable {
    cursor: not-allowed;

    svg {
      fill: var(--brand-neutrals-gray-medium);
    }
  }
}
.total-row {
  font-weight: bold;
  background-color: #9d9b9b;
  color: #333;
}
.dropdown-bank-count {
  width: 20% !important;
}
