import { FC, useState } from "react";
import Table from "../MetlifeComponents/Table/Table";
import { mockColumns, mockData } from "./mttoConceptMockData";
import { TableData } from "../MetlifeComponents/Table/table-types";

type MttoConceptProps = {};

const MttoConcept: FC<MttoConceptProps> = () => {
  const [conceptData, setConceptData] = useState<TableData[]>(mockData);

  const onSaveConcept = (newElement: TableData) => {
    // Generate new ID for the new element
    const newId = Math.max(...conceptData.map(item => Number(item.id))) + 1;
    const elementWithId = {
      ...newElement,
      id: newId.toString(),
      deleteActionExtraProps: {
        "aria-label": "Borrar",
        "data-tooltip": "Borrar",
      },
      editActionExtraProps: {
        id: `edit-action-${newId}`,
        "aria-label": "Editar",
        "data-tooltip": "Editar",
      },
    };

    // Add new element at the beginning of the array
    setConceptData([elementWithId, ...conceptData]);
  };

  const onEditConcept = (editElement: TableData) => {
    setConceptData(conceptData.map(item =>
      item.id === editElement.id ? { ...item, ...editElement } : item
    ));
  };

  const onDeleteConcept = (elementId: number) => {
    setConceptData(conceptData.filter(item => Number(item.id) !== elementId));
  };

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE CONCEPTOS</h1>
      <Table
        id="concepts"
        data={conceptData}
        columns={mockColumns}
        filters={["company", "area", "concept"]}
        onSave={onSaveConcept}
        onEdit={onEditConcept}
        onDelete={onDeleteConcept}
      />
    </div>
  );
};

export default MttoConcept;
