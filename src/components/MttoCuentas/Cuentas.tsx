import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableColumn,
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";
import { TableInputType } from "../../enums";
import { mockData, bankList, companyList, monedaList } from "./cuentasMockData";
import { walkUpBindingElementsAndPatterns } from "typescript";

type CuentaProps = {};

const URL_BANK = "v1/services/treasury/bank-accounts-maintenance";

const Cuentas: FC<CuentaProps> = () => {

  const bankingCountCol: TableColumn[] = [
    {
      id: "table-head-1",
      label: "ID",
      accessor: "id",
      isLink: false,
      sortable: true,
      hidden: true,
      editable: false,
      type: TableInputType.NUMBER,
      isCompositeKey:false,
    },
    {
      id: "table-head-2",
      label: "COMPAÑIA",
      accessor: "tctaCveCia",
      isLink: false,
      sortable: true,
      hidden: true,
      editable: true,
      type: TableInputType.SELECT,
      displayOptions: companyList,
      isCompositeKey: true,
    },
    {
      id: "table-head-3",
      label: "BANCO",
      accessor: "tctaIdEntidad",
      isLink: false,
      sortable: true,
      hidden: true,
      editable: true,
      type: TableInputType.SELECT,
      displayOptions: bankList,
      isCompositeKey: true,
    },
    {
      id: "table-head-4",
      label: "MONEDA",
      accessor: "tcefCveMoneda",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.SELECT,
      displayOptions: monedaList,
    },
    {
      id: "table-head-5",
      label: "NUMERO CUENTA",
      accessor: "tcefNumCuenta",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
      isCompositeKey: true,
    },
    {
      id: "table-head-6",
      label: "CUARTO NIVEL CONTABLE",
      accessor: "tctaAuxCont",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-7",
      label: "NUMERO CHEQUE",
      accessor: "tctaNumCheque",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-8",
      label: "SUCURSAL",
      accessor: "tctaIdSucursal",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-9",
      label: "DIGITO VERIFICADOR",
      accessor: "tctaCalDigito",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.NUMBER,
    },
    {
      id: "table-head-10",
      label: "PROTECCION CHEQUE",
      accessor: "tctaProtCke",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: true,
      type: TableInputType.TEXT,
    },
    
    {
      id: "table-head-11",
      label: "USUARIO CREO",
      accessor: "tctaUserCreo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-12",
      label: "FECHA CREO",
      accessor: "tctaFecCreo",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.DATE_TIME,
    },
    {
      id: "table-head-13",
      label: "USUARIO MOD",
      accessor: "tctaUserMod",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.TEXT,
    },
    {
      id: "table-head-14",
      label: "FECHA MOD",
      accessor: "tctaFecMod",
      isLink: false,
      sortable: true,
      hidden: false,
      editable: false,
      type: TableInputType.DATE_TIME,
    },
  ];

  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);
  
    const { result: responsePost } = usePost(callPostAPI);
    const { result, loading, error, refetch } = useGet<TableResponse>({
      url: URL_BANK,
      call: true,
    });
    const onSaveCountBanking = (newElement: TableData) => {
      setCallPostAPI({
        url: URL_BANK,
        call: true,
        data: newElement,
      });
    };
    const onEditCountBanking = (editElement: TableData) => {
      setCallPostAPI({
        url: `${URL_BANK}/${editElement?.id}`,
        call: true,
        method: "PUT",
        data: editElement,
      });
    };
    const onDeleteCountBanking = (
      params: { type: "composite"; [key:string]: string | number | undefined } | {type: "id"; id: number}) => {
      if(params.type === "composite") {
        const compositeKeys = bankingCountCol
        .filter(col => col.isCompositeKey)
        .map(col => col.accessor);
        const values = compositeKeys.map(key => params[key]);
        if (values.some(v => v === undefined)) {
          console.error("Faltan valores en la clave compuesta: ", compositeKeys, params);
          return;
        }
        const safeValues = values as (string | number)[];
        const urlPath = safeValues.join("/");
        console.log("" + URL_BANK+"/" +urlPath);  
        setCallPostAPI({
          url: `${URL_BANK}/${urlPath}`,
          call: true,
          method: "DELETE",
        });
      } else if (params.type === "id"){
        setCallPostAPI({
          url: `${URL_BANK}/${params.id}`,
          call: true,
          method: "DELETE",
        });
      }
    };  
  
    /*const banksData = bankList
    ?.map((b) => {
      return {
        label: b.TCEF_ID_ENTIDAD,
        value: b.TCEF_NOMBRE_CORTO,  
      };
    })*/
    
  

  

    useEffect(() => {
        if (responsePost != null) {
          refetch();
          setCallPostAPI(initialPostApi);
        }
      }, [responsePost, refetch]);
    
    if (loading)
        return (
          <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
        );
      if (error) return <>{`Error: ${error.message}`}</>;  

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE CUENTAS BANCARIAS</h1>
        <Table
          id="cuentas"
          data={result?.data ?? []}
          columns={bankingCountCol}
          filters={["tctaCveCia","tctaIdEntidad","tcefNumCuenta"]}
          onSave={onSaveCountBanking}
          onEdit={onEditCountBanking}
          onDelete={onDeleteCountBanking}
        />
    </div>
  );
};

export default Cuentas;
