import { TableData } from "../MetlifeComponents/Table/table-types";

const mockData: TableData[] = [
  {
    ID: "1",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338008",
    TCTA_AUX_CONT: "423",
    TCTA_NUM_CHEQUE: "7878534",
    TCTA_ID_SUCURSAL: "519990029",
    TCTA_CAL_DIGITO: "0023",
    TCTA_PROT_CKE: "S",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "1",
  },
  {
    ID: "2",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "0188014191",
    TCTA_AUX_CONT: "436",
    TCTA_NUM_CHEQUE: "6373",
    TCTA_ID_SUCURSAL: "511150179",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: "USER_MOD",
    TCTA_FEC_MOD: "15/04/2025",
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "2",
  },
  {
    ID: "3",
    TCEF_CVE_MONEDA: "MN",
    TCEF_NUM_CUENTA: "2338016",
    TCTA_AUX_CONT: "444",
    TCTA_NUM_CHEQUE: "",
    TCTA_ID_SUCURSAL: "",
    TCTA_CAL_DIGITO: "",
    TCTA_PROT_CKE: "",
    TCTA_USER_CREO: "USER",
    TCTA_FEC_CREO: "01/04/2025",
    TCTA_USER_MOD: null,
    TCTA_FEC_MOD: null,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
    id: "3",
  },
];

const bankList = [
  {ariaLabel: "BANAMEX", label: "BANAMEX", value: "BNM"},
  {ariaLabel: "BBVA", label: "BBVA", value: "BBVA"},
];

const companyList = [
  {ariaLabel: "Metlife Mexico", label: "Metlife", value: "1"},
  {ariaLabel: "Excelencia Operativa", label: "2", value: "2"},
  {ariaLabel: "Metlife Pensiones", label: "3", value: "3"},
  {ariaLabel: "Metlife LA Ases", label: "4", value: "4"},
];

const monedaList = [
  {ariaLabel: "Moneda Nacional", label: "MN", value: "MN"},
  {ariaLabel: "Dolares", label: "DL", value: "DL"},
  {ariaLabel: "Euros", label: "EU", value: "EU"},
];

export { mockData, bankList, companyList, monedaList };
