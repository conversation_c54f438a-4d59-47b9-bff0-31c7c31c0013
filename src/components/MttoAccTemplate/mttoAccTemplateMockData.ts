import { TableInputType } from "../../enums";
import { TableColumn, TableData } from "../MetlifeComponents/Table/table-types";
import { DropDownOption } from "../MetlifeComponents/DropDown/DropDownOption";

// Dropdown options for select fields
const companyOptions: DropDownOption[] = [
  { ariaLabel: "BMW", label: "BMW", value: "BMW" },
  { ariaLabel: "Audi", label: "Audi", value: "Audi" },
  { ariaLabel: "Metlife", label: "Metlife", value: "Metlife" },
  { ariaLabel: "Toyota", label: "Toyota", value: "Toyota" },
  { ariaLabel: "Honda", label: "Honda", value: "Honda" },
  { ariaLabel: "Ford", label: "Ford", value: "Ford" },
  { ariaLabel: "Chevrolet", label: "Chevrolet", value: "Chevrolet" },
  { ariaLabel: "Nissan", label: "Nissan", value: "Nissan" },
  { ariaLabel: "Mazda", label: "Mazda", value: "Mazda" },
  { ariaLabel: "Kia", label: "Kia", value: "Kia" },
];

const areaOptions: DropDownOption[] = [
  { ariaLabel: "B1-SGM-Foraneo", label: "B1-SGM-Foraneo", value: "B1-SGM-Foraneo" },
  { ariaLabel: "B2-SGM-Local", label: "B2-SGM-Local", value: "B2-SGM-Local" },
  { ariaLabel: "B3-SGM-Central", label: "B3-SGM-Central", value: "B3-SGM-Central" },
  { ariaLabel: "B4-SGM-Norte", label: "B4-SGM-Norte", value: "B4-SGM-Norte" },
  { ariaLabel: "B5-SGM-Sur", label: "B5-SGM-Sur", value: "B5-SGM-Sur" },
  { ariaLabel: "B6-SGM-Este", label: "B6-SGM-Este", value: "B6-SGM-Este" },
  { ariaLabel: "B7-SGM-Oeste", label: "B7-SGM-Oeste", value: "B7-SGM-Oeste" },
  { ariaLabel: "B8-SGM-Noreste", label: "B8-SGM-Noreste", value: "B8-SGM-Noreste" },
];

const moduleOptions: DropDownOption[] = [
  { ariaLabel: "1234", label: "1234", value: "1234" },
  { ariaLabel: "2345", label: "2345", value: "2345" },
  { ariaLabel: "3456", label: "3456", value: "3456" },
  { ariaLabel: "4567", label: "4567", value: "4567" },
  { ariaLabel: "5678", label: "5678", value: "5678" },
  { ariaLabel: "6789", label: "6789", value: "6789" },
];

const conceptOptions: DropDownOption[] = [
  { ariaLabel: "PU80", label: "PU80", value: "PU80" },
  { ariaLabel: "PU81", label: "PU81", value: "PU81" },
  { ariaLabel: "PU82", label: "PU82", value: "PU82" },
  { ariaLabel: "PU83", label: "PU83", value: "PU83" },
  { ariaLabel: "PU84", label: "PU84", value: "PU84" },
  { ariaLabel: "PU85", label: "PU85", value: "PU85" },
  { ariaLabel: "PU86", label: "PU86", value: "PU86" },
  { ariaLabel: "PU87", label: "PU87", value: "PU87" },
];

const movementTypeOptions: DropDownOption[] = [
  { ariaLabel: "Tipo 1", label: "Tipo 1", value: "1" },
  { ariaLabel: "Tipo 2", label: "Tipo 2", value: "2" },
  { ariaLabel: "Tipo 3", label: "Tipo 3", value: "3" },
  { ariaLabel: "Tipo 4", label: "Tipo 4", value: "4" },
  { ariaLabel: "Tipo 5", label: "Tipo 5", value: "5" },
  { ariaLabel: "Tipo 6", label: "Tipo 6", value: "6" },
  { ariaLabel: "Tipo 7", label: "Tipo 7", value: "7" },
  { ariaLabel: "Tipo 8", label: "Tipo 8", value: "8" },
];

const accountingEntryOptions: DropDownOption[] = [
  { ariaLabel: "Asiento 1", label: "Asiento 1", value: "1" },
  { ariaLabel: "Asiento 2", label: "Asiento 2", value: "2" },
  { ariaLabel: "Asiento 3", label: "Asiento 3", value: "3" },
  { ariaLabel: "Asiento 4", label: "Asiento 4", value: "4" },
  { ariaLabel: "Asiento 5", label: "Asiento 5", value: "5" },
  { ariaLabel: "Asiento 6", label: "Asiento 6", value: "6" },
  { ariaLabel: "Asiento 7", label: "Asiento 7", value: "7" },
  { ariaLabel: "Asiento 8", label: "Asiento 8", value: "8" },
];

const mockColumns: TableColumn[] = [
  {
    id: "table-head-0",
    label: "ID",
    accessor: "id",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: false,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-1",
    label: "COMPAÑÍA",
    accessor: "company",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.SELECT,
    displayOptions: companyOptions,
  },
  {
    id: "table-head-2",
    label: "ÁREA",
    accessor: "area",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.SELECT,
    displayOptions: areaOptions,
  },
  {
    id: "table-head-3",
    label: "MÓDULO",
    accessor: "module",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.SELECT,
    displayOptions: moduleOptions,
  },
  {
    id: "table-head-4",
    label: "CONCEPTO",
    accessor: "concept",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.SELECT,
    displayOptions: conceptOptions,
  },
  {
    id: "table-head-5",
    label: "ID PLANTILLA",
    accessor: "templateId",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-6",
    label: "DESCRIPCIÓN",
    accessor: "description",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.TEXT,
  },
  {
    id: "table-head-7",
    label: "TIPO DE MOVIMIENTO",
    accessor: "movementType",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: true,
    showInFormOnly: true,
    type: TableInputType.SELECT,
    displayOptions: movementTypeOptions,
  },
  {
    id: "table-head-8",
    label: "ASIENTO CONTABLE",
    accessor: "accountingEntry",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.SELECT,
    displayOptions: accountingEntryOptions,
  },
  {
    id: "table-head-9",
    label: "CUENTA",
    accessor: "account",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-10",
    label: "SUBCUENTA",
    accessor: "subAccount",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-11",
    label: "SUBSUBCUENTA",
    accessor: "subSubAccount",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-12",
    label: "AUXILIAR 1",
    accessor: "auxiliar1",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-13",
    label: "AUXILIAR 2",
    accessor: "auxiliar2",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
  {
    id: "table-head-14",
    label: "SEXTO NIVEL",
    accessor: "sixthLevel",
    isLink: false,
    sortable: true,
    hidden: false,
    editable: true,
    showInNew: false,
    showInFormOnly: false,
    type: TableInputType.NUMBER,
  },
];

const mockData: TableData[] = [
  {
    id: "1",
    company: "BMW",
    area: "B1-SGM-Foraneo",
    module: "1234",
    concept: "PU80",
    templateId: 2,
    description: "Pago de siniestros de gastos medicos",
    movementType: "4",
    accountingEntry: "6",
    account: 7,
    subAccount: 1,
    subSubAccount: 23,
    auxiliar1: 12,
    auxiliar2: 32,
    sixthLevel: 1,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-1",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "2",
    company: "Audi",
    area: "B2-SGM-Local",
    module: "2345",
    concept: "PU81",
    templateId: 3,
    description: "Pago de siniestros de vida",
    movementType: "5",
    accountingEntry: "7",
    account: 8,
    subAccount: 2,
    subSubAccount: 24,
    auxiliar1: 13,
    auxiliar2: 33,
    sixthLevel: 2,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-2",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "3",
    company: "Metlife",
    area: "B3-SGM-Central",
    module: "3456",
    concept: "PU82",
    templateId: 4,
    description: "Pago de siniestros de autos",
    movementType: "6",
    accountingEntry: "8",
    account: 9,
    subAccount: 3,
    subSubAccount: 25,
    auxiliar1: 14,
    auxiliar2: 34,
    sixthLevel: 3,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-3",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "4",
    company: "Toyota",
    area: "B4-SGM-Norte",
    module: "4567",
    concept: "PU83",
    templateId: 5,
    description: "Pago de siniestros de hogar",
    movementType: "7",
    accountingEntry: "1",
    account: 10,
    subAccount: 4,
    subSubAccount: 26,
    auxiliar1: 15,
    auxiliar2: 35,
    sixthLevel: 4,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-4",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "5",
    company: "Honda",
    area: "B5-SGM-Sur",
    module: "5678",
    concept: "PU84",
    templateId: 6,
    description: "Pago de siniestros de viaje",
    movementType: "8",
    accountingEntry: "2",
    account: 11,
    subAccount: 5,
    subSubAccount: 27,
    auxiliar1: 16,
    auxiliar2: 36,
    sixthLevel: 5,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-5",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "6",
    company: "Ford",
    area: "B6-SGM-Este",
    module: "6789",
    concept: "PU85",
    templateId: 7,
    description: "Pago de siniestros de accidentes",
    movementType: "1",
    accountingEntry: "3",
    account: 12,
    subAccount: 6,
    subSubAccount: 28,
    auxiliar1: 17,
    auxiliar2: 37,
    sixthLevel: 6,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-6",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "7",
    company: "Chevrolet",
    area: "B7-SGM-Oeste",
    module: "1234",
    concept: "PU86",
    templateId: 8,
    description: "Pago de siniestros de responsabilidad civil",
    movementType: "2",
    accountingEntry: "4",
    account: 13,
    subAccount: 7,
    subSubAccount: 29,
    auxiliar1: 18,
    auxiliar2: 38,
    sixthLevel: 7,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-7",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "8",
    company: "Nissan",
    area: "B8-SGM-Noreste",
    module: "2345",
    concept: "PU87",
    templateId: 9,
    description: "Pago de siniestros de incendio",
    movementType: "3",
    accountingEntry: "5",
    account: 14,
    subAccount: 8,
    subSubAccount: 30,
    auxiliar1: 19,
    auxiliar2: 39,
    sixthLevel: 8,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-8",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "9",
    company: "Mazda",
    area: "B9-SGM-Noroeste",
    module: "3456",
    concept: "PU88",
    templateId: 10,
    description: "Pago de siniestros de robo",
    movementType: "4",
    accountingEntry: "6",
    account: 15,
    subAccount: 9,
    subSubAccount: 31,
    auxiliar1: 20,
    auxiliar2: 40,
    sixthLevel: 9,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-9",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "10",
    company: "Kia",
    area: "B10-SGM-Sureste",
    module: "4567",
    concept: "PU89",
    templateId: 11,
    description: "Pago de siniestros de transporte",
    movementType: "5",
    accountingEntry: "7",
    account: 16,
    subAccount: 10,
    subSubAccount: 32,
    auxiliar1: 21,
    auxiliar2: 41,
    sixthLevel: 10,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-10",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "11",
    company: "Hyundai",
    area: "B11-SGM-Suroeste",
    module: "5678",
    concept: "PU90",
    templateId: 12,
    description: "Pago de siniestros de maquinaria",
    movementType: "6",
    accountingEntry: "8",
    account: 17,
    subAccount: 11,
    subSubAccount: 33,
    auxiliar1: 22,
    auxiliar2: 42,
    sixthLevel: 11,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-11",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "12",
    company: "Volkswagen",
    area: "B12-SGM-Centro",
    module: "6789",
    concept: "PU91",
    templateId: 13,
    description: "Pago de siniestros de construcción",
    movementType: "7",
    accountingEntry: "1",
    account: 18,
    subAccount: 12,
    subSubAccount: 34,
    auxiliar1: 23,
    auxiliar2: 43,
    sixthLevel: 12,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-12",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "13",
    company: "Subaru",
    area: "B13-SGM-Periférico",
    module: "1234",
    concept: "PU92",
    templateId: 14,
    description: "Pago de siniestros de agricultura",
    movementType: "8",
    accountingEntry: "2",
    account: 19,
    subAccount: 13,
    subSubAccount: 35,
    auxiliar1: 24,
    auxiliar2: 44,
    sixthLevel: 13,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-13",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "14",
    company: "Mitsubishi",
    area: "B14-SGM-Metropolitano",
    module: "2345",
    concept: "PU93",
    templateId: 15,
    description: "Pago de siniestros de ganadería",
    movementType: "1",
    accountingEntry: 19,
    account: 20,
    subAccount: 14,
    subSubAccount: 36,
    auxiliar1: 25,
    auxiliar2: 45,
    sixthLevel: 14,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-14",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "15",
    company: "Lexus",
    area: "B15-SGM-Rural",
    module: "3456",
    concept: "PU94",
    templateId: 16,
    description: "Pago de siniestros de pesca",
    movementType: "2",
    accountingEntry: 20,
    account: 21,
    subAccount: 15,
    subSubAccount: 37,
    auxiliar1: 26,
    auxiliar2: 46,
    sixthLevel: 15,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-15",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "16",
    company: "Infiniti",
    area: "B16-SGM-Urbano",
    module: "4567",
    concept: "PU95",
    templateId: 17,
    description: "Pago de siniestros de minería",
    movementType: "3",
    accountingEntry: 21,
    account: 22,
    subAccount: 16,
    subSubAccount: 38,
    auxiliar1: 27,
    auxiliar2: 47,
    sixthLevel: 16,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-16",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "17",
    company: "Acura",
    area: "B17-SGM-Industrial",
    module: "5678",
    concept: "PU96",
    templateId: 18,
    description: "Pago de siniestros de energía",
    movementType: "4",
    accountingEntry: 22,
    account: 23,
    subAccount: 17,
    subSubAccount: 39,
    auxiliar1: 28,
    auxiliar2: 48,
    sixthLevel: 17,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-17",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "18",
    company: "Genesis",
    area: "B18-SGM-Comercial",
    module: "6789",
    concept: "PU97",
    templateId: 19,
    description: "Pago de siniestros de tecnología",
    movementType: "5",
    accountingEntry: 23,
    account: 24,
    subAccount: 18,
    subSubAccount: 40,
    auxiliar1: 29,
    auxiliar2: 49,
    sixthLevel: 18,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-18",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "19",
    company: "Lincoln",
    area: "B19-SGM-Residencial",
    module: "1234",
    concept: "PU98",
    templateId: 20,
    description: "Pago de siniestros de comunicaciones",
    movementType: "6",
    accountingEntry: 24,
    account: 25,
    subAccount: 19,
    subSubAccount: 41,
    auxiliar1: 30,
    auxiliar2: 50,
    sixthLevel: 19,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-19",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
  {
    id: "20",
    company: "Cadillac",
    area: "B20-SGM-Corporativo",
    module: "2345",
    concept: "PU99",
    templateId: 21,
    description: "Pago de siniestros de servicios financieros",
    movementType: "7",
    accountingEntry: 25,
    account: 26,
    subAccount: 20,
    subSubAccount: 42,
    auxiliar1: 31,
    auxiliar2: 51,
    sixthLevel: 20,
    deleteActionExtraProps: {
      "aria-label": "Borrar",
      "data-tooltip": "Borrar",
    },
    editActionExtraProps: {
      id: "edit-action-20",
      "aria-label": "Editar",
      "data-tooltip": "Editar",
    },
  },
];

export { mockData, mockColumns, accountingEntryOptions };
