import { FC, useEffect, useState } from "react";
import {
  initialPostApi,
  useGet,
  usePost,
  UsePostProps,
} from "../../hooks/useFetch";
import Table from "../MetlifeComponents/Table/Table";
import {
  TableData,
  TableResponse,
} from "../MetlifeComponents/Table/table-types";
import MLDCReactLoadingIndicator from "@metlife/mldc-react-loading-indicator";

type RolProps = {};

const URL_ROLE = "v1/services/treasury/catalogs/roles";

const Rol: FC<RolProps> = () => {
  const [callPostAPI, setCallPostAPI] = useState<UsePostProps>(initialPostApi);

  const { result: responsePost } = usePost(callPostAPI);
  const { result, loading, error, refetch } = useGet<TableResponse>({
    url: URL_ROLE,
    call: true,
  });

  const onSaveRol = (newElement: TableData) => {
    setCallPostAPI({
      url: URL_ROLE,
      call: true,
      data: newElement,
    });
  };

  const onEditRol = (editElement: TableData) => {
    setCallPostAPI({
      url: `${URL_ROLE}/${editElement?.id}`,
      call: true,
      method: "PUT",
      data: editElement,
    });
  };

  const onDeleteRol = (
    params: { type: "composite"; [key:string]: string | number | undefined } | {type: "id"; id: number}) => {
    if(params.type === "composite") {
      const compositeKeys = (result?.columns ?? [])
      .filter(col => col.isCompositeKey)
      .map(col => col.accessor);
      const values = compositeKeys.map(key => params[key]);
      if (values.some(v => v === undefined)) {
        console.error("Faltan valores en la clave compuesta: ", compositeKeys, params);
        return;
      }
      const safeValues = values as (string | number)[];
      const urlPath = safeValues.join("/");  
      setCallPostAPI({
        url: `${URL_ROLE}/${urlPath}`,
        call: true,
        method: "DELETE",
      });
    } else if (params.type === "id"){
      setCallPostAPI({
        url: `${URL_ROLE}/${params.id}`,
        call: true,
        method: "DELETE",
      });
    }
  };

  useEffect(() => {
    if (responsePost != null) {
      refetch();
      setCallPostAPI(initialPostApi);
    }
  }, [responsePost, refetch]);

  if (loading)
    return (
      <MLDCReactLoadingIndicator id="Loading" className="loading-wrapper" />
    );
  if (error) return <>{`Error: ${error.message}`}</>;

  return (
    <div className="container">
      <h1 className="title">MANTENIMIENTO DE ROLES</h1>
      <Table
        id="Roles"
        data={result?.data ?? []}
        columns={result?.columns ?? []}
        filters={["id", "trol_NOMBRE"]}
        onSave={onSaveRol}
        onEdit={onEditRol}
        onDelete={onDeleteRol}
      />
    </div>
  );
};

export default Rol;
